<script setup lang="ts">

import {onMounted, ref} from "vue";

/*type ItemList = {
  id: number
  name: string
}

const list = ref<ItemList[]>([])

list.value = [
  {
    id: 1,
    name: 'a'
  },
  {
    id: 2,
    name: 'b'
  }
]*/

/*const count = ref(100)
console.log(count)*/

/*
const year = ref<string | number>(2023)
year.value = '2023'*/

/*type GoodsType = {
  id: number
  name: string
  price: number
}

const goodsList = ref<GoodsType[]>([])
goodsList.value = [
  {
    id: 1,
    name: 'a',
    price: 100
  },
  {
    id: 2,
    name: 'b',
    price: 200
  }
]*/

/*const inputChange = (e) => {
  console.log(e)
}*/

/*const inputChange = (e: Event) => {
  console.log((e.target as HTMLInputElement).value)
}*/

const elRef = ref<HTMLInputElement,true>(null)
onMounted(() => {
  elRef.value.focus()
})


</script>

<template>
  <!--  <ul>
      <li v-for="item in list" :key="item.id">
        {{ item.name }}
      </li>
    </ul>-->

  <!--  <input type="text" @change="inputChange">-->

  <input type="text" ref="elRef">
</template>

<style scoped>
</style>
